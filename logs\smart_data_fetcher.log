2025-06-04 18:25:10,961 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:25:10,961 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:25:51,378 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:25:51,378 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:27:04,065 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:27:04,067 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:27:29,764 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:27:44,415 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-04-01 to 2025-04-01)
2025-06-04 18:27:45,678 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.ANS1 (2025-04-01 to 2025-04-09)
2025-06-04 18:27:58,585 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:28:08,170 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-04-01 to 2025-04-01)
2025-06-04 18:28:09,191 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.ANSP (2025-04-01 to 2025-04-09)
2025-06-04 18:28:09,528 - WARNING - smart_data_fetcher - smart_data_fetcher.py:77 - Plant type not found for Combined View, defaulting to solar
2025-06-04 18:30:21,431 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:30:21,432 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:30:21,438 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.ANS1 (2025-04-01 to 2025-04-09)
2025-06-04 18:31:59,222 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:31:59,223 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:32:12,036 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.KIDS (2025-04-01 to 2025-04-09)
2025-06-04 18:32:20,772 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:32:20,774 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:32:20,774 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.KIDS (2025-04-01 to 2025-04-09)
2025-06-04 18:33:55,575 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:33:55,575 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:33:55,579 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.KIDS (2025-04-01 to 2025-04-09)
2025-06-04 18:35:17,318 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:35:17,318 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:35:17,318 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 9 rows for IN.INTE.KIDS (2025-04-01 to 2025-04-09)
2025-06-04 18:36:49,697 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:36:49,697 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:36:49,707 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:36:50,029 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:37:32,343 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:37:32,344 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:37:32,353 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:37:32,654 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:38:10,418 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:38:10,419 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:38:10,439 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:38:10,737 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:39:32,449 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:39:32,449 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:39:32,468 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:39:32,759 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:39:40,340 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:39:40,340 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:39:55,672 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.APIP (2025-01-01 to 2025-01-01)
2025-06-04 18:40:13,852 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:40:13,852 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:40:13,860 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.APIP (2025-01-01 to 2025-01-01)
2025-06-04 18:41:03,111 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:41:03,111 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:41:03,120 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.APIP (2025-01-01 to 2025-01-01)
2025-06-04 18:42:42,299 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:42:42,299 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:42:42,318 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:42:42,589 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
2025-06-04 18:43:53,589 - INFO - smart_data_fetcher - smart_data_fetcher.py:38 - API integration initialized successfully
2025-06-04 18:43:53,589 - INFO - smart_data_fetcher - smart_data_fetcher.py:49 - Plants information loaded successfully
2025-06-04 18:43:53,606 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANS1 (2025-01-01 to 2025-01-01)
2025-06-04 18:43:53,875 - INFO - smart_data_fetcher - smart_data_fetcher.py:154 - Retrieved 96 rows for IN.INTE.ANSP (2025-01-01 to 2025-01-01)
