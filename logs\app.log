2025-06-04 18:28:52,345 - ERROR - app - app.py:211 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:28:52,345 - ERROR - app - app.py:211 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:28:52,346 - ERROR - app - app.py:212 - Traceback (most recent call last):
  File "D:\<PERSON><PERSON><PERSON><PERSON><PERSON>\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0].date() == start_date.date() and
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:28:52,346 - ERROR - app - app.py:212 - Traceback (most recent call last):
  File "D:\<PERSON><PERSON><PERSON><PERSON>an\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0].date() == start_date.date() and
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:32:12,505 - ERROR - app - app.py:220 - Application error: 'generation_kwh'
2025-06-04 18:32:12,505 - ERROR - app - app.py:220 - Application error: 'generation_kwh'
2025-06-04 18:32:12,655 - ERROR - app - app.py:221 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in display_daily_generation_consumption_view
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 10374, in apply
    return op.apply().__finalize__(self, method="apply")
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 916, in apply
    return self.apply_standard()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in <lambda>
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                                                           ~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:32:12,655 - ERROR - app - app.py:221 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in display_daily_generation_consumption_view
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 10374, in apply
    return op.apply().__finalize__(self, method="apply")
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 916, in apply
    return self.apply_standard()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in <lambda>
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                                                           ~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:32:21,573 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:32:21,573 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:32:21,586 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in display_daily_generation_consumption_view
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 10374, in apply
    return op.apply().__finalize__(self, method="apply")
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 916, in apply
    return self.apply_standard()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in <lambda>
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                                                           ~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:32:21,586 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in display_daily_generation_consumption_view
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 10374, in apply
    return op.apply().__finalize__(self, method="apply")
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 916, in apply
    return self.apply_standard()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1206, in <lambda>
    df['surplus_generation'] = df.apply(lambda row: max(0, row['generation_kwh'] - row['consumption_kwh']), axis=1)
                                                           ~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:32:56,491 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:32:56,491 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:32:56,491 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:32:56,491 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:33:56,081 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:33:56,081 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:33:56,081 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1245, in display_daily_generation_consumption_view
    df['generation_after_loss_kwh'] = df['generation_kwh'].apply(apply_generation_loss)
                                      ~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:33:56,081 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 152, in main
    display_daily_generation_consumption_view(display_plant_name, start_date, end_date, section="summary")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1245, in display_daily_generation_consumption_view
    df['generation_after_loss_kwh'] = df['generation_kwh'].apply(apply_generation_loss)
                                      ~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:35:33,256 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:35:33,256 - ERROR - app - app.py:225 - Application error: 'generation_kwh'
2025-06-04 18:35:33,256 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 191, in main
    display_daily_tod_binned_view(display_plant_name, start_date, end_date, section="tod")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1412, in display_daily_tod_binned_view
    regular_total_gen = regular_df['generation_kwh'].sum()
                        ~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:35:33,256 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'generation_kwh'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 191, in main
    display_daily_tod_binned_view(display_plant_name, start_date, end_date, section="tod")
  File "D:\Harikrishnan\Plot Generation V1\src\display_components.py", line 1412, in display_daily_tod_binned_view
    regular_total_gen = regular_df['generation_kwh'].sum()
                        ~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'generation_kwh'

2025-06-04 18:38:55,189 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:38:55,189 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:38:55,190 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:38:55,190 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:42:55,618 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:42:55,618 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:43:42,211 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:43:42,211 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

2025-06-04 18:43:59,124 - ERROR - app - app.py:225 - Application error: 'datetime.date' object has no attribute 'date'
2025-06-04 18:43:59,124 - ERROR - app - app.py:226 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation V1\app.py", line 72, in main
    data_summary['date_range'][0] == start_date.date() and
                                     ^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'date'

