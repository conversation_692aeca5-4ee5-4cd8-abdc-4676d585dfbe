2025-06-04 18:25:10,660 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:25:10,961 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-06-04 18:25:31,916 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANSP generation, fetching from API
2025-06-04 18:25:51,161 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:25:51,380 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-06-04 18:26:11,868 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANSP generation, fetching from API
2025-06-04 18:27:03,842 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:27:04,067 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-06-04 18:27:12,939 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-06-04 18:27:14,001 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-06-04 18:27:29,745 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANS1 generation (96 rows)
2025-06-04 18:27:30,060 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANSP generation, fetching from API
2025-06-04 18:27:44,406 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANS1 generation (96 rows)
2025-06-04 18:27:44,672 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANSP generation, fetching from API
2025-06-04 18:27:45,670 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANS1 generation (9 rows)
2025-06-04 18:27:45,947 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANSP generation, fetching from API
2025-06-04 18:27:58,582 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANSP generation (96 rows)
2025-06-04 18:28:08,161 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANSP generation (96 rows)
2025-06-04 18:28:09,173 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANSP generation (9 rows)
2025-06-04 18:28:09,528 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for Combined View generation, fetching from API
2025-06-04 18:30:21,205 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:30:21,433 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-04-01 to 2025-04-09)
2025-06-04 18:31:58,998 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:31:59,225 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-06-04 18:32:12,022 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (9 rows)
2025-06-04 18:32:20,506 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:32:20,774 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-04-01 to 2025-04-09)
2025-06-04 18:33:55,329 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:33:55,577 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-04-01 to 2025-04-09)
2025-06-04 18:35:17,114 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:35:17,318 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-04-01 to 2025-04-09)
2025-06-04 18:36:49,478 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:36:49,698 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:36:50,023 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:37:32,120 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:37:32,345 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:37:32,651 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:38:10,210 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:38:10,422 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:38:10,736 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:39:32,220 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:39:32,449 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:39:32,756 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:39:40,095 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:39:40,340 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.APIP generation, fetching from API
2025-06-04 18:39:55,655 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.APIP generation (96 rows)
2025-06-04 18:40:13,612 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:40:13,852 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.APIP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:41:02,898 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:41:03,111 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.APIP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:42:42,069 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:42:42,299 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:42:42,586 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
2025-06-04 18:43:53,356 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 18:43:53,589 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANS1 generation (2025-01-01 to 2025-01-01)
2025-06-04 18:43:53,870 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.ANSP generation (2025-01-01 to 2025-01-01)
