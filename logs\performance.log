2025-06-04 18:27:58,862 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:08,443 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:09,510 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:09,521 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:30:21,764 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:12,289 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:12,325 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:21,256 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:21,256 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:33:55,841 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.001 seconds
2025-06-04 18:33:55,846 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.004 seconds
2025-06-04 18:35:17,590 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:35:17,594 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.003 seconds
