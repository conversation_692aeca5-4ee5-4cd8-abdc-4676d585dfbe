2025-06-04 18:27:58,862 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:08,443 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:09,510 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:28:09,521 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:30:21,764 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:12,289 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:12,325 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:21,256 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:32:21,256 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:33:55,841 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.001 seconds
2025-06-04 18:33:55,846 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.004 seconds
2025-06-04 18:35:17,590 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:35:17,594 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.003 seconds
2025-06-04 18:36:50,326 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:36:50,326 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:37:32,931 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:37:32,934 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:38:11,032 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:38:11,036 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:39:33,018 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.001 seconds
2025-06-04 18:39:33,021 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:39:56,155 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:40:14,121 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:41:03,429 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:42:42,869 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:42:42,869 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
2025-06-04 18:43:54,158 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.001 seconds
2025-06-04 18:43:54,162 - INFO - performance - performance_utils.py:78 - Function 'tod_binning' executed in 0.000 seconds
