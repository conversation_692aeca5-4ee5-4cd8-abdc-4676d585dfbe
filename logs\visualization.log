2025-06-04 18:28:40,055 - INFO - visualization - visualization.py:1812 - Combined wind and solar data shape: (18, 5)
2025-06-04 18:28:40,056 - INFO - visualization - visualization.py:1813 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:28:40,061 - INFO - visualization - visualization.py:1814 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-02 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-02 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 21346.0, 1: 16943.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-06-04 18:28:40,063 - INFO - visualization - visualization.py:1866 - Grouping data by date and source
2025-06-04 18:28:40,065 - INFO - visualization - visualization.py:1873 - Pivoting data
2025-06-04 18:28:40,082 - INFO - visualization - visualization.py:1990 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:30:42,135 - INFO - visualization - visualization.py:1812 - Combined wind and solar data shape: (18, 5)
2025-06-04 18:30:42,136 - INFO - visualization - visualization.py:1813 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:30:42,137 - INFO - visualization - visualization.py:1814 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-02 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-02 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 21346.0, 1: 16943.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-06-04 18:30:42,137 - INFO - visualization - visualization.py:1866 - Grouping data by date and source
2025-06-04 18:30:42,140 - INFO - visualization - visualization.py:1873 - Pivoting data
2025-06-04 18:30:42,153 - INFO - visualization - visualization.py:1990 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:32:12,340 - ERROR - visualization - visualization.py:619 - Error creating daily comparison plot: 'generation_kwh'
2025-06-04 18:32:21,296 - ERROR - visualization - visualization.py:619 - Error creating daily comparison plot: 'generation_kwh'
2025-06-04 18:32:23,024 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 210440.89 kWh, Max Gen Value: 33322.30 kWh
2025-06-04 18:32:23,024 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 36, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 18:32:23,027 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1291.86 kWh per 15-min interval
2025-06-04 18:33:55,854 - ERROR - visualization - visualization.py:619 - Error creating daily comparison plot: 'generation_kwh'
2025-06-04 18:35:17,603 - ERROR - visualization - visualization.py:619 - Error creating daily comparison plot: 'generation_kwh'
2025-06-04 18:35:32,286 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 194202.00 kWh, Max Gen Value: 29768.00 kWh
2025-06-04 18:35:32,287 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 9
2025-06-04 18:38:41,692 - INFO - visualization - visualization.py:1812 - Combined wind and solar data shape: (192, 5)
2025-06-04 18:38:41,692 - INFO - visualization - visualization.py:1813 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:38:41,695 - INFO - visualization - visualization.py:1814 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-01-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-01-01 00:15:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-01-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-01-01 00:15:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: nan, 1: nan}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-06-04 18:38:41,697 - INFO - visualization - visualization.py:1843 - Using time column for single day 15-minute data
2025-06-04 18:38:41,697 - INFO - visualization - visualization.py:1851 - Grouping single day data by time and source
2025-06-04 18:38:41,701 - INFO - visualization - visualization.py:1855 - Pivoting single day data
2025-06-04 18:38:41,724 - INFO - visualization - visualization.py:1990 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:40:29,678 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 613427.20 kWh, Max Gen Value: 12950.84 kWh
2025-06-04 18:40:29,678 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 18:40:49,235 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 613427.20 kWh, Max Gen Value: 241811.19 kWh
2025-06-04 18:40:49,235 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 18:40:49,236 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 11017.37 kWh per 15-min interval
2025-06-04 18:41:24,162 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 613427.20 kWh, Max Gen Value: 12950.84 kWh
2025-06-04 18:41:24,162 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 18:51:01,881 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 18:51:01,887 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 18:51:13,364 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 18:51:13,370 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 18:51:13,372 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 18:51:13,373 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 18:51:13,374 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 18:54:56,113 - INFO - visualization - visualization.py:1812 - Combined wind and solar data shape: (192, 5)
2025-06-04 18:54:56,113 - INFO - visualization - visualization.py:1813 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:54:56,114 - INFO - visualization - visualization.py:1814 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 0.0, 1: 0.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-06-04 18:54:56,114 - INFO - visualization - visualization.py:1843 - Using time column for single day 15-minute data
2025-06-04 18:54:56,114 - INFO - visualization - visualization.py:1851 - Grouping single day data by time and source
2025-06-04 18:54:56,114 - INFO - visualization - visualization.py:1855 - Pivoting single day data
2025-06-04 18:54:56,131 - INFO - visualization - visualization.py:1990 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:55:28,632 - INFO - visualization - visualization.py:1812 - Combined wind and solar data shape: (192, 5)
2025-06-04 18:55:28,632 - INFO - visualization - visualization.py:1813 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:55:28,632 - INFO - visualization - visualization.py:1814 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 0.0, 1: 0.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-06-04 18:55:28,632 - INFO - visualization - visualization.py:1843 - Using time column for single day 15-minute data
2025-06-04 18:55:28,632 - INFO - visualization - visualization.py:1851 - Grouping single day data by time and source
2025-06-04 18:55:28,638 - INFO - visualization - visualization.py:1855 - Pivoting single day data
2025-06-04 18:55:28,650 - INFO - visualization - visualization.py:1990 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:55:41,158 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 21346.09 kWh, Max Gen Value: 17929.12 kWh
2025-06-04 18:55:41,159 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 18:55:41,160 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 560.28 kWh per 15-min interval
