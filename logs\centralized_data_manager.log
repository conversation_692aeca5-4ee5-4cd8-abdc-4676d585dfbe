2025-06-04 18:22:47,631 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:22:49,187 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:17,454 - INFO - centralized_data_manager - centralized_data_manager.py:604 - C<PERSON> cleared
2025-06-04 18:24:17,530 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:17,976 - INFO - centralized_data_manager - centralized_data_manager.py:604 - C<PERSON> cleared
2025-06-04 18:24:18,043 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,150 - INFO - centralized_data_manager - centralized_data_manager.py:604 - <PERSON><PERSON> cleared
2025-06-04 18:24:18,217 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,308 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:18,382 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,458 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:18,532 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,611 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:18,686 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,772 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:18,860 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:18,939 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:19,003 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:19,078 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:19,146 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:19,222 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:19,306 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:24:19,446 - INFO - centralized_data_manager - centralized_data_manager.py:604 - Cache cleared
2025-06-04 18:24:19,518 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:25:09,032 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:25:10,653 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/Combined View
2025-06-04 18:25:10,655 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 2 plants: ['ANS', 'A N S Paper Mills Pvt Ltd']
2025-06-04 18:25:10,655 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:25:10,655 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:25:31,634 - WARNING - centralized_data_manager - centralized_data_manager.py:170 - API EMPTY: No generation data found for ANS after 20.98s
2025-06-04 18:25:31,638 - WARNING - centralized_data_manager - centralized_data_manager.py:356 - No generation data for ANS
2025-06-04 18:25:31,639 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:25:31,916 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.28s
2025-06-04 18:25:31,916 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:25:31,916 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: A N S Paper Mills Pvt Ltd
2025-06-04 18:25:31,916 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:49,227 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:25:51,146 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/Combined View
2025-06-04 18:25:51,146 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 2 plants: ['ANS', 'A N S Paper Mills Pvt Ltd']
2025-06-04 18:25:51,146 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:25:51,146 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:25:51,712 - WARNING - centralized_data_manager - centralized_data_manager.py:170 - API EMPTY: No generation data found for A N S Paper Mills Pvt Ltd after 19.80s
2025-06-04 18:25:51,712 - WARNING - centralized_data_manager - centralized_data_manager.py:356 - No generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:51,712 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:52,050 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for A N S Paper Mills Pvt Ltd after 0.34s
2025-06-04 18:25:52,050 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:52,050 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:25:52,050 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 41.40s
2025-06-04 18:25:52,050 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 0 generation, 0 consumption
2025-06-04 18:25:52,050 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 0 generation, 0 consumption plants
2025-06-04 18:26:11,608 - WARNING - centralized_data_manager - centralized_data_manager.py:170 - API EMPTY: No generation data found for ANS after 20.46s
2025-06-04 18:26:11,608 - WARNING - centralized_data_manager - centralized_data_manager.py:356 - No generation data for ANS
2025-06-04 18:26:11,608 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:26:11,866 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.26s
2025-06-04 18:26:11,867 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:26:11,867 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: A N S Paper Mills Pvt Ltd
2025-06-04 18:26:11,868 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,432 - WARNING - centralized_data_manager - centralized_data_manager.py:170 - API EMPTY: No generation data found for A N S Paper Mills Pvt Ltd after 35.56s
2025-06-04 18:26:47,432 - WARNING - centralized_data_manager - centralized_data_manager.py:356 - No generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,432 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,751 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for A N S Paper Mills Pvt Ltd after 0.32s
2025-06-04 18:26:47,752 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,752 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:26:47,753 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 56.61s
2025-06-04 18:26:47,753 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 0 generation, 0 consumption
2025-06-04 18:26:47,753 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 0 generation, 0 consumption plants
2025-06-04 18:27:02,294 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:27:03,838 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/Combined View
2025-06-04 18:27:03,839 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 2 plants: ['ANS', 'A N S Paper Mills Pvt Ltd']
2025-06-04 18:27:03,839 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:27:03,840 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:27:12,939 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/Combined View
2025-06-04 18:27:12,939 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 2 plants: ['ANS', 'A N S Paper Mills Pvt Ltd']
2025-06-04 18:27:12,939 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:27:12,939 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:27:13,986 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/Combined View
2025-06-04 18:27:13,986 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 2 plants: ['ANS', 'A N S Paper Mills Pvt Ltd']
2025-06-04 18:27:14,000 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:27:14,000 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:27:29,764 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: ANS - 96 rows in 25.92s
2025-06-04 18:27:29,769 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for ANS: 96 rows
2025-06-04 18:27:29,770 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:27:30,058 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.29s
2025-06-04 18:27:30,059 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:27:30,059 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: A N S Paper Mills Pvt Ltd
2025-06-04 18:27:30,059 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:44,416 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: ANS - 96 rows in 31.48s
2025-06-04 18:27:44,418 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for ANS: 96 rows
2025-06-04 18:27:44,418 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:27:44,670 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.25s
2025-06-04 18:27:44,671 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:27:44,671 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: A N S Paper Mills Pvt Ltd
2025-06-04 18:27:44,672 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:45,679 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: ANS - 9 rows in 31.68s
2025-06-04 18:27:45,681 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for ANS: 9 rows
2025-06-04 18:27:45,681 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:27:45,945 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.26s
2025-06-04 18:27:45,945 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:27:45,945 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: A N S Paper Mills Pvt Ltd
2025-06-04 18:27:45,947 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:58,586 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: A N S Paper Mills Pvt Ltd - 96 rows in 28.53s
2025-06-04 18:27:58,589 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for A N S Paper Mills Pvt Ltd: 96 rows
2025-06-04 18:27:58,589 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:58,860 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for A N S Paper Mills Pvt Ltd after 0.27s
2025-06-04 18:27:58,861 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:58,862 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:27:58,862 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for ANS
2025-06-04 18:28:08,170 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: A N S Paper Mills Pvt Ltd - 96 rows in 23.50s
2025-06-04 18:28:08,173 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for A N S Paper Mills Pvt Ltd: 96 rows
2025-06-04 18:28:08,173 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,443 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for A N S Paper Mills Pvt Ltd after 0.27s
2025-06-04 18:28:08,443 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,443 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:28:08,443 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for ANS
2025-06-04 18:28:09,191 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: A N S Paper Mills Pvt Ltd - 9 rows in 23.24s
2025-06-04 18:28:09,195 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for A N S Paper Mills Pvt Ltd: 9 rows
2025-06-04 18:28:09,195 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,510 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for A N S Paper Mills Pvt Ltd after 0.32s
2025-06-04 18:28:09,510 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,510 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:28:09,510 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for ANS
2025-06-04 18:28:09,510 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for ANS
2025-06-04 18:28:09,510 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,523 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,523 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 55.54s
2025-06-04 18:28:09,523 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 2 generation, 0 consumption
2025-06-04 18:28:09,523 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 2 generation, 0 consumption plants
2025-06-04 18:30:21,185 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:30:21,200 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for ANS Paper Mills Pvt Ltd/ANS
2025-06-04 18:30:21,201 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 1 plants: ['ANS']
2025-06-04 18:30:21,201 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: ANS
2025-06-04 18:30:21,202 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for ANS
2025-06-04 18:30:21,439 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: ANS - 9 rows in 0.24s
2025-06-04 18:30:21,441 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for ANS: 9 rows
2025-06-04 18:30:21,441 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for ANS
2025-06-04 18:30:21,749 - WARNING - centralized_data_manager - centralized_data_manager.py:214 - API EMPTY: No consumption data found for ANS after 0.31s
2025-06-04 18:30:21,749 - WARNING - centralized_data_manager - centralized_data_manager.py:369 - No consumption data for ANS
2025-06-04 18:30:21,749 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:30:21,763 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for ANS
2025-06-04 18:30:21,767 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for ANS
2025-06-04 18:30:21,767 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 0.57s
2025-06-04 18:30:21,767 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 1 generation, 0 consumption
2025-06-04 18:30:21,767 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 1 generation, 0 consumption plants
2025-06-04 18:31:58,956 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:31:58,989 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for Kids Clinic India Limited/Kids Clinic India Limited
2025-06-04 18:31:58,993 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 1 plants: ['Kids Clinic India Limited']
2025-06-04 18:31:58,994 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: Kids Clinic India Limited
2025-06-04 18:31:58,994 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for Kids Clinic India Limited
2025-06-04 18:32:12,036 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: Kids Clinic India Limited - 9 rows in 13.04s
2025-06-04 18:32:12,040 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for Kids Clinic India Limited: 9 rows
2025-06-04 18:32:12,040 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for Kids Clinic India Limited
2025-06-04 18:32:12,272 - INFO - centralized_data_manager - centralized_data_manager.py:199 - API SUCCESS: Kids Clinic India Limited consumption - 864 rows in 0.23s
2025-06-04 18:32:12,272 - INFO - centralized_data_manager - centralized_data_manager.py:367 - Consumption data cached for Kids Clinic India Limited: 864 rows
2025-06-04 18:32:12,287 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:32:12,289 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for Kids Clinic India Limited
2025-06-04 18:32:12,334 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for Kids Clinic India Limited
2025-06-04 18:32:12,336 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 13.35s
2025-06-04 18:32:12,336 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 1 generation, 1 consumption
2025-06-04 18:32:12,337 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 1 generation, 1 consumption plants
2025-06-04 18:32:20,493 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:32:20,506 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for Kids Clinic India Limited/Kids Clinic India Limited
2025-06-04 18:32:20,506 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 1 plants: ['Kids Clinic India Limited']
2025-06-04 18:32:20,506 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: Kids Clinic India Limited
2025-06-04 18:32:20,506 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for Kids Clinic India Limited
2025-06-04 18:32:20,774 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: Kids Clinic India Limited - 9 rows in 0.27s
2025-06-04 18:32:20,774 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for Kids Clinic India Limited: 9 rows
2025-06-04 18:32:20,774 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for Kids Clinic India Limited
2025-06-04 18:32:21,241 - INFO - centralized_data_manager - centralized_data_manager.py:199 - API SUCCESS: Kids Clinic India Limited consumption - 864 rows in 0.47s
2025-06-04 18:32:21,256 - INFO - centralized_data_manager - centralized_data_manager.py:367 - Consumption data cached for Kids Clinic India Limited: 864 rows
2025-06-04 18:32:21,256 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:32:21,256 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for Kids Clinic India Limited
2025-06-04 18:32:21,273 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for Kids Clinic India Limited
2025-06-04 18:32:21,273 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 0.77s
2025-06-04 18:32:21,273 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 1 generation, 1 consumption
2025-06-04 18:32:21,273 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 1 generation, 1 consumption plants
2025-06-04 18:33:55,289 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:33:55,329 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for Kids Clinic India Limited/Kids Clinic India Limited
2025-06-04 18:33:55,329 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 1 plants: ['Kids Clinic India Limited']
2025-06-04 18:33:55,329 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: Kids Clinic India Limited
2025-06-04 18:33:55,329 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for Kids Clinic India Limited
2025-06-04 18:33:55,579 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: Kids Clinic India Limited - 9 rows in 0.25s
2025-06-04 18:33:55,584 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for Kids Clinic India Limited: 9 rows
2025-06-04 18:33:55,585 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for Kids Clinic India Limited
2025-06-04 18:33:55,829 - INFO - centralized_data_manager - centralized_data_manager.py:199 - API SUCCESS: Kids Clinic India Limited consumption - 864 rows in 0.24s
2025-06-04 18:33:55,836 - INFO - centralized_data_manager - centralized_data_manager.py:367 - Consumption data cached for Kids Clinic India Limited: 864 rows
2025-06-04 18:33:55,836 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:33:55,840 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for Kids Clinic India Limited
2025-06-04 18:33:55,850 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for Kids Clinic India Limited
2025-06-04 18:33:55,850 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 0.52s
2025-06-04 18:33:55,851 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 1 generation, 1 consumption
2025-06-04 18:33:55,851 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 1 generation, 1 consumption plants
2025-06-04 18:35:17,071 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:35:17,111 - INFO - centralized_data_manager - centralized_data_manager.py:311 - Starting SINGLE API CALL strategy for Kids Clinic India Limited/Kids Clinic India Limited
2025-06-04 18:35:17,112 - INFO - centralized_data_manager - centralized_data_manager.py:337 - Fetching data for 1 plants: ['Kids Clinic India Limited']
2025-06-04 18:35:17,112 - INFO - centralized_data_manager - centralized_data_manager.py:345 - Processing plant: Kids Clinic India Limited
2025-06-04 18:35:17,113 - INFO - centralized_data_manager - centralized_data_manager.py:138 - API CALL: Fetching generation data for Kids Clinic India Limited
2025-06-04 18:35:17,318 - INFO - centralized_data_manager - centralized_data_manager.py:155 - API SUCCESS: Kids Clinic India Limited - 9 rows in 0.21s
2025-06-04 18:35:17,333 - INFO - centralized_data_manager - centralized_data_manager.py:354 - Generation data cached for Kids Clinic India Limited: 9 rows
2025-06-04 18:35:17,334 - INFO - centralized_data_manager - centralized_data_manager.py:188 - API CALL: Fetching consumption data for Kids Clinic India Limited
2025-06-04 18:35:17,568 - INFO - centralized_data_manager - centralized_data_manager.py:199 - API SUCCESS: Kids Clinic India Limited consumption - 864 rows in 0.23s
2025-06-04 18:35:17,585 - INFO - centralized_data_manager - centralized_data_manager.py:367 - Consumption data cached for Kids Clinic India Limited: 864 rows
2025-06-04 18:35:17,585 - INFO - centralized_data_manager - centralized_data_manager.py:374 - Processing cached data for visualizations...
2025-06-04 18:35:17,590 - INFO - centralized_data_manager - centralized_data_manager.py:389 - Combined data processed for Kids Clinic India Limited
2025-06-04 18:35:17,596 - INFO - centralized_data_manager - centralized_data_manager.py:399 - ToD data processed for Kids Clinic India Limited
2025-06-04 18:35:17,596 - INFO - centralized_data_manager - centralized_data_manager.py:413 - Data fetch completed in 0.48s
2025-06-04 18:35:17,596 - INFO - centralized_data_manager - centralized_data_manager.py:414 - Success summary: 1 generation, 1 consumption
2025-06-04 18:35:17,596 - INFO - centralized_data_manager - centralized_data_manager.py:415 - Cached data ready for 1 generation, 1 consumption plants
