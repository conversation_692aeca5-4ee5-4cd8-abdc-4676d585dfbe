2025-06-04 18:22:47,381 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:22:47,381 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:22:47,609 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:08,693 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:25:08,694 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:25:09,007 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:20,946 - WARNING - data - data.py:1468 - Smart cache returned empty data for ANS, falling back to original method
2025-06-04 18:25:20,947 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANS1 for API calls (display name: ANS)
2025-06-04 18:25:20,948 - INFO - data - data.py:1556 - Fetching data for plant: ANS (ID: IN.INTE.ANS1) from 2025-06-04 to 2025-06-04
2025-06-04 18:25:20,948 - INFO - data - data.py:1560 - Fetching solar plant data with category: Plant, parameter: Daily Energy
2025-06-04 18:25:31,634 - INFO - data - data.py:1571 - Solar data fetch completed for ANS
2025-06-04 18:25:31,634 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['ANS.Daily Energy']
2025-06-04 18:25:31,634 - WARNING - data - data.py:1637 - API returned empty dataframe for ANS
2025-06-04 18:25:31,639 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-06-04
2025-06-04 18:25:31,895 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:25:41,778 - WARNING - data - data.py:1468 - Smart cache returned empty data for A N S Paper Mills Pvt Ltd, falling back to original method
2025-06-04 18:25:41,778 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANSP for API calls (display name: A N S Paper Mills Pvt Ltd)
2025-06-04 18:25:41,778 - INFO - data - data.py:1556 - Fetching data for plant: A N S Paper Mills Pvt Ltd (ID: IN.INTE.ANSP) from 2025-06-04 to 2025-06-04
2025-06-04 18:25:41,778 - INFO - data - data.py:1574 - Fetching wind plant data with category: Turbine, parameter: WTUR.Generation today
2025-06-04 18:25:48,893 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:25:48,893 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:25:49,206 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:51,712 - INFO - data - data.py:1585 - Wind data fetch completed for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:51,712 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['T02.Generation today']
2025-06-04 18:25:51,712 - WARNING - data - data.py:1637 - API returned empty dataframe for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:51,712 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-06-04
2025-06-04 18:25:52,029 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:26:01,181 - WARNING - data - data.py:1468 - Smart cache returned empty data for ANS, falling back to original method
2025-06-04 18:26:01,181 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANS1 for API calls (display name: ANS)
2025-06-04 18:26:01,181 - INFO - data - data.py:1556 - Fetching data for plant: ANS (ID: IN.INTE.ANS1) from 2025-06-04 to 2025-06-04
2025-06-04 18:26:01,181 - INFO - data - data.py:1560 - Fetching solar plant data with category: Plant, parameter: Daily Energy
2025-06-04 18:26:11,608 - INFO - data - data.py:1571 - Solar data fetch completed for ANS
2025-06-04 18:26:11,608 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['ANS.Daily Energy']
2025-06-04 18:26:11,608 - WARNING - data - data.py:1637 - API returned empty dataframe for ANS
2025-06-04 18:26:11,608 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-06-04
2025-06-04 18:26:11,847 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:26:29,894 - WARNING - data - data.py:1468 - Smart cache returned empty data for A N S Paper Mills Pvt Ltd, falling back to original method
2025-06-04 18:26:29,894 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANSP for API calls (display name: A N S Paper Mills Pvt Ltd)
2025-06-04 18:26:29,894 - INFO - data - data.py:1556 - Fetching data for plant: A N S Paper Mills Pvt Ltd (ID: IN.INTE.ANSP) from 2025-06-04 to 2025-06-04
2025-06-04 18:26:29,894 - INFO - data - data.py:1574 - Fetching wind plant data with category: Turbine, parameter: WTUR.Generation today
2025-06-04 18:26:47,432 - INFO - data - data.py:1585 - Wind data fetch completed for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,432 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['T02.Generation today']
2025-06-04 18:26:47,432 - WARNING - data - data.py:1637 - API returned empty dataframe for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,439 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-06-04
2025-06-04 18:26:47,730 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:27:02,026 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:27:02,026 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:27:02,267 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:27:29,764 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:27:29,770 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:27:30,022 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:44,416 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:27:44,418 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-01
2025-06-04 18:27:44,650 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:45,679 - INFO - data - data.py:1465 - Smart cache returned 9 rows for ANS
2025-06-04 18:27:45,682 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:27:45,923 - WARNING - data - data.py:624 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:58,585 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:58,590 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:27:58,841 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,170 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,173 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-04-01
2025-06-04 18:28:08,421 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,191 - INFO - data - data.py:1465 - Smart cache returned 9 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,195 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:09,493 - WARNING - data - data.py:624 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:10,063 - WARNING - data - data.py:1468 - Smart cache returned empty data for Combined View, falling back to original method
2025-06-04 18:28:10,063 - INFO - data - data.py:1534 - Combined View is not a real plant, handling separately
2025-06-04 18:28:10,063 - WARNING - data - data.py:1818 - No generation data found for Combined View between 2025-04-01 and 2025-04-09
2025-06-04 18:28:10,072 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:28:10,072 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:28:10,072 - INFO - data - data.py:2081 - Using daily granularity for date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:10,072 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:28:25,896 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (9, 2)
2025-06-04 18:28:25,907 - INFO - data - data.py:2155 - Added 9 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:28:25,907 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:28:40,036 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (9, 2)
2025-06-04 18:28:40,040 - INFO - data - data.py:2230 - Added 9 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:28:40,041 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:28:40,042 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:28:40,042 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:28:40,042 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 18 rows
2025-06-04 18:28:40,741 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: Combined View, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:40,995 - WARNING - data - data.py:624 - No data found for plant_id Combined View, trying with Plant Long Name Combined View
2025-06-04 18:30:20,960 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:30:20,960 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:30:21,181 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:30:21,439 - INFO - data - data.py:1465 - Smart cache returned 9 rows for ANS
2025-06-04 18:30:21,442 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:30:21,740 - WARNING - data - data.py:624 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:30:21,775 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:30:21,775 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:30:21,775 - INFO - data - data.py:2081 - Using daily granularity for date range: 2025-04-01 to 2025-04-09
2025-06-04 18:30:21,775 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:30:32,198 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (9, 2)
2025-06-04 18:30:32,206 - INFO - data - data.py:2155 - Added 9 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:30:32,206 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:30:42,119 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (9, 2)
2025-06-04 18:30:42,124 - INFO - data - data.py:2230 - Added 9 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:30:42,125 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:30:42,126 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:30:42,126 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:30:42,126 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 18 rows
2025-06-04 18:30:42,825 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-01
2025-06-04 18:30:42,825 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:30:42,826 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:30:52,433 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:30:52,449 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-01T00:00:00Z+05:30', '1': '2025-04-01T00:15:00Z+05:30', '2': '2025-04-01T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:30:52,459 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:30:52,465 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 21346.09 kWh
2025-06-04 18:30:52,465 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:30:52,466 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-01
2025-06-04 18:30:52,933 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:30:52,969 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 25750.09 kWh
2025-06-04 18:30:52,970 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:30:52,973 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 21346.09 kWh, Consumption: 0.00 kWh
2025-06-04 18:30:52,973 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:30:52,973 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 21346.09 kWh
2025-06-04 18:30:52,973 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:30:52,973 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:30:52,973 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:30:52,973 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 21346.09 kWh
2025-06-04 18:30:52,973 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:30:52,973 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:30:52,973 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-01
2025-06-04 18:30:52,973 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-01
2025-06-04 18:30:53,384 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:30:53,423 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-01
2025-06-04 18:30:53,423 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:30:53,423 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:30:53,423 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:30:53,423 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:30:53,423 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:30:53,423 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:30:53,423 - INFO - data - data.py:1156 - Before merge - Generation: 21346.09 kWh, Consumption: 0.00 kWh
2025-06-04 18:30:53,433 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:30:53,433 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:30:53,435 - INFO - data - data.py:1176 - After merge - Generation: 21346.09 kWh, Consumption: 0.00 kWh
2025-06-04 18:30:53,435 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 21346.09 kWh, Total consumption: 0.00 kWh
2025-06-04 18:30:53,436 - INFO - data - data.py:1192 - ToD - Filtered out 10 rows with BOTH zero generation AND consumption
2025-06-04 18:30:53,436 - INFO - data - data.py:1196 - ToD - Using filtered data with 14 valid hours
2025-06-04 18:30:53,436 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:30:53,437 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 21346.09 kWh
2025-06-04 18:30:53,437 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:30:53,438 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:30:53,446 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 21346.09 kWh
2025-06-04 18:30:53,451 - INFO - data - data.py:1251 - Grouped data shape: (3, 4)
2025-06-04 18:30:53,455 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:30:53,461 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:30:53,463 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 21346.09 kWh
2025-06-04 18:30:53,469 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:30:53,471 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-02
2025-06-04 18:30:53,474 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:30:53,476 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:03,348 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:03,348 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-02T00:00:00Z+05:30', '1': '2025-04-02T00:15:00Z+05:30', '2': '2025-04-02T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:03,352 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:03,353 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 16943.00 kWh
2025-06-04 18:31:03,354 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:03,355 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-02
2025-06-04 18:31:03,593 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:03,614 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 21347.00 kWh
2025-06-04 18:31:03,614 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:03,620 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 16943.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:03,620 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:03,621 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 16943.00 kWh
2025-06-04 18:31:03,621 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:03,621 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:03,621 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:03,622 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 16943.00 kWh
2025-06-04 18:31:03,622 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:03,622 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:03,622 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-02
2025-06-04 18:31:03,622 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-02
2025-06-04 18:31:03,893 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:03,911 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-02
2025-06-04 18:31:03,912 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:03,912 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:03,912 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:03,913 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:03,913 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:03,913 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:03,914 - INFO - data - data.py:1156 - Before merge - Generation: 16943.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:03,915 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:03,915 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:03,915 - INFO - data - data.py:1176 - After merge - Generation: 16943.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:03,917 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 16943.00 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:03,917 - INFO - data - data.py:1192 - ToD - Filtered out 9 rows with BOTH zero generation AND consumption
2025-06-04 18:31:03,917 - INFO - data - data.py:1196 - ToD - Using filtered data with 15 valid hours
2025-06-04 18:31:03,918 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:03,919 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 16943.00 kWh
2025-06-04 18:31:03,919 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:03,919 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:03,921 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 16943.00 kWh
2025-06-04 18:31:03,922 - INFO - data - data.py:1251 - Grouped data shape: (4, 4)
2025-06-04 18:31:03,922 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:03,923 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:03,924 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 16943.00 kWh
2025-06-04 18:31:03,924 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:03,924 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-03
2025-06-04 18:31:03,924 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:03,924 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:13,837 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:13,837 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-03T00:00:00Z+05:30', '1': '2025-04-03T00:15:00Z+05:30', '2': '2025-04-03T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:13,856 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:13,859 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 16698.00 kWh
2025-06-04 18:31:13,860 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:13,866 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-03
2025-06-04 18:31:14,126 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:14,140 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 21102.00 kWh
2025-06-04 18:31:14,140 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:14,140 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 16698.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:14,140 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:14,140 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 16698.00 kWh
2025-06-04 18:31:14,140 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:14,140 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:14,140 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:14,152 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 16698.00 kWh
2025-06-04 18:31:14,152 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:14,152 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:14,152 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-03
2025-06-04 18:31:14,153 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-03
2025-06-04 18:31:14,386 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:14,416 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-03
2025-06-04 18:31:14,417 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:14,417 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:14,417 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:14,417 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:14,417 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:14,418 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:14,418 - INFO - data - data.py:1156 - Before merge - Generation: 16698.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:14,420 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:14,420 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:14,421 - INFO - data - data.py:1176 - After merge - Generation: 16698.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:14,422 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 16698.00 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:14,422 - INFO - data - data.py:1192 - ToD - Filtered out 9 rows with BOTH zero generation AND consumption
2025-06-04 18:31:14,422 - INFO - data - data.py:1196 - ToD - Using filtered data with 15 valid hours
2025-06-04 18:31:14,423 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:14,426 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 16698.00 kWh
2025-06-04 18:31:14,426 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:14,426 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:14,429 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 16698.00 kWh
2025-06-04 18:31:14,429 - INFO - data - data.py:1251 - Grouped data shape: (4, 4)
2025-06-04 18:31:14,430 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:14,431 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:14,431 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 16698.00 kWh
2025-06-04 18:31:14,431 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:14,433 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-04
2025-06-04 18:31:14,433 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:14,434 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:24,512 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:24,512 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-04T00:00:00Z+05:30', '1': '2025-04-04T00:15:00Z+05:30', '2': '2025-04-04T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:24,520 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:24,522 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 41121.65 kWh
2025-06-04 18:31:24,523 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:24,525 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-04
2025-06-04 18:31:24,749 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:24,772 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 45525.65 kWh
2025-06-04 18:31:24,772 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:24,772 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 41121.65 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:24,772 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:24,772 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 41121.65 kWh
2025-06-04 18:31:24,772 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:24,772 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:24,772 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:24,772 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 41121.65 kWh
2025-06-04 18:31:24,772 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:24,772 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:24,772 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-04
2025-06-04 18:31:24,784 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-04
2025-06-04 18:31:25,014 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:25,037 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-04
2025-06-04 18:31:25,037 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:25,037 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:25,037 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:25,037 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:25,037 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:25,037 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:25,037 - INFO - data - data.py:1156 - Before merge - Generation: 41121.65 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:25,037 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:25,037 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:25,037 - INFO - data - data.py:1176 - After merge - Generation: 41121.65 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:25,037 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 41121.65 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:25,037 - INFO - data - data.py:1192 - ToD - Filtered out 9 rows with BOTH zero generation AND consumption
2025-06-04 18:31:25,037 - INFO - data - data.py:1196 - ToD - Using filtered data with 15 valid hours
2025-06-04 18:31:25,047 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:25,048 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 41121.65 kWh
2025-06-04 18:31:25,049 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:25,049 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:25,050 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 41121.65 kWh
2025-06-04 18:31:25,050 - INFO - data - data.py:1251 - Grouped data shape: (4, 4)
2025-06-04 18:31:25,050 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:25,052 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:25,053 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 41121.65 kWh
2025-06-04 18:31:25,054 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:25,054 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-05
2025-06-04 18:31:25,055 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:25,055 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:34,618 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:34,618 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-05T00:00:00Z+05:30', '1': '2025-04-05T00:15:00Z+05:30', '2': '2025-04-05T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:34,618 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:34,618 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 19675.08 kWh
2025-06-04 18:31:34,618 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:34,630 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-05
2025-06-04 18:31:34,848 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:34,864 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 24079.08 kWh
2025-06-04 18:31:34,864 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:34,884 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 19675.08 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:34,884 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:34,885 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 19675.08 kWh
2025-06-04 18:31:34,885 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:34,885 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:34,887 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:34,887 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 19675.08 kWh
2025-06-04 18:31:34,887 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:34,887 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:34,888 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-05
2025-06-04 18:31:34,888 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-05
2025-06-04 18:31:35,098 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:35,120 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-05
2025-06-04 18:31:35,120 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:35,120 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:35,120 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:35,120 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:35,120 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:35,120 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:35,120 - INFO - data - data.py:1156 - Before merge - Generation: 19675.08 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:35,120 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:35,120 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:35,120 - INFO - data - data.py:1176 - After merge - Generation: 19675.08 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:35,120 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 19675.08 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:35,120 - INFO - data - data.py:1192 - ToD - Filtered out 11 rows with BOTH zero generation AND consumption
2025-06-04 18:31:35,120 - INFO - data - data.py:1196 - ToD - Using filtered data with 13 valid hours
2025-06-04 18:31:35,120 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:35,134 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 19675.08 kWh
2025-06-04 18:31:35,134 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:35,134 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:35,136 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 19675.08 kWh
2025-06-04 18:31:35,137 - INFO - data - data.py:1251 - Grouped data shape: (3, 4)
2025-06-04 18:31:35,137 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:35,139 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:35,139 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 19675.08 kWh
2025-06-04 18:31:35,139 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:35,140 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-06
2025-06-04 18:31:35,140 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:35,141 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:44,704 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:44,719 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-06T00:00:00Z+05:30', '1': '2025-04-06T00:15:00Z+05:30', '2': '2025-04-06T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:44,730 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:44,730 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 19757.07 kWh
2025-06-04 18:31:44,730 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:44,737 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-06
2025-06-04 18:31:45,071 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:45,103 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 24161.07 kWh
2025-06-04 18:31:45,103 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:45,103 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 19757.07 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:45,103 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:45,103 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 19757.07 kWh
2025-06-04 18:31:45,103 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:45,103 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:45,103 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:45,119 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 19757.07 kWh
2025-06-04 18:31:45,119 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:45,120 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:45,120 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-06
2025-06-04 18:31:45,120 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-06
2025-06-04 18:31:45,504 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:45,553 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-06
2025-06-04 18:31:45,553 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:45,553 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:45,553 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:45,553 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:45,553 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:45,553 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:45,553 - INFO - data - data.py:1156 - Before merge - Generation: 19757.07 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:45,553 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:45,553 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:45,553 - INFO - data - data.py:1176 - After merge - Generation: 19757.07 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:45,553 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 19757.07 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:45,553 - INFO - data - data.py:1192 - ToD - Filtered out 10 rows with BOTH zero generation AND consumption
2025-06-04 18:31:45,553 - INFO - data - data.py:1196 - ToD - Using filtered data with 14 valid hours
2025-06-04 18:31:45,553 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:45,553 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 19757.07 kWh
2025-06-04 18:31:45,553 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:45,553 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:45,553 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 19757.07 kWh
2025-06-04 18:31:45,568 - INFO - data - data.py:1251 - Grouped data shape: (3, 4)
2025-06-04 18:31:45,568 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:45,571 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:45,571 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 19757.07 kWh
2025-06-04 18:31:45,571 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:45,571 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-07
2025-06-04 18:31:45,571 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:45,571 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:55,388 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:31:55,388 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-07T00:00:00Z+05:30', '1': '2025-04-07T00:15:00Z+05:30', '2': '2025-04-07T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:31:55,388 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:31:55,388 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 36762.53 kWh
2025-06-04 18:31:55,388 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:31:55,404 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-07
2025-06-04 18:31:55,705 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:55,726 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 41166.53 kWh
2025-06-04 18:31:55,726 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:31:55,727 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 36762.53 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:55,727 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:31:55,727 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 36762.53 kWh
2025-06-04 18:31:55,727 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:31:55,727 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:31:55,732 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:31:55,732 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 36762.53 kWh
2025-06-04 18:31:55,732 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:31:55,732 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:31:55,733 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-07
2025-06-04 18:31:55,733 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-07
2025-06-04 18:31:55,972 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:31:56,003 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-07
2025-06-04 18:31:56,005 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:31:56,006 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:31:56,006 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:31:56,007 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:56,007 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:31:56,007 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:31:56,009 - INFO - data - data.py:1156 - Before merge - Generation: 36762.53 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:56,011 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:31:56,011 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:31:56,012 - INFO - data - data.py:1176 - After merge - Generation: 36762.53 kWh, Consumption: 0.00 kWh
2025-06-04 18:31:56,015 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 36762.53 kWh, Total consumption: 0.00 kWh
2025-06-04 18:31:56,016 - INFO - data - data.py:1192 - ToD - Filtered out 10 rows with BOTH zero generation AND consumption
2025-06-04 18:31:56,016 - INFO - data - data.py:1196 - ToD - Using filtered data with 14 valid hours
2025-06-04 18:31:56,016 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:31:56,016 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 36762.53 kWh
2025-06-04 18:31:56,016 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:31:56,017 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:31:56,018 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 36762.53 kWh
2025-06-04 18:31:56,018 - INFO - data - data.py:1251 - Grouped data shape: (3, 4)
2025-06-04 18:31:56,020 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:31:56,024 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:31:56,025 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 36762.53 kWh
2025-06-04 18:31:56,025 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:31:56,026 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-08
2025-06-04 18:31:56,027 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:31:56,027 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:31:58,721 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:31:58,721 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:31:58,955 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:32:11,007 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:32:11,007 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-08T00:00:00Z+05:30', '1': '2025-04-08T00:15:00Z+05:30', '2': '2025-04-08T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:32:11,023 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:32:11,023 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 19289.00 kWh
2025-06-04 18:32:11,023 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:32:11,041 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-08
2025-06-04 18:32:11,338 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:32:11,373 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 23693.00 kWh
2025-06-04 18:32:11,373 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:32:11,376 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 19289.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:11,376 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:32:11,376 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 19289.00 kWh
2025-06-04 18:32:11,377 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:32:11,377 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:32:11,378 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:32:11,379 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 19289.00 kWh
2025-06-04 18:32:11,379 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:32:11,379 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:32:11,379 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-08
2025-06-04 18:32:11,380 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-08
2025-06-04 18:32:11,605 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:32:11,627 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-08
2025-06-04 18:32:11,627 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:32:11,627 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:32:11,627 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:32:11,627 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:32:11,627 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:32:11,627 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:32:11,627 - INFO - data - data.py:1156 - Before merge - Generation: 19289.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:11,627 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:32:11,627 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:32:11,627 - INFO - data - data.py:1176 - After merge - Generation: 19289.00 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:11,627 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 19289.00 kWh, Total consumption: 0.00 kWh
2025-06-04 18:32:11,627 - INFO - data - data.py:1192 - ToD - Filtered out 9 rows with BOTH zero generation AND consumption
2025-06-04 18:32:11,627 - INFO - data - data.py:1196 - ToD - Using filtered data with 15 valid hours
2025-06-04 18:32:11,627 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:32:11,635 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 19289.00 kWh
2025-06-04 18:32:11,635 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:32:11,635 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:32:11,637 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 19289.00 kWh
2025-06-04 18:32:11,637 - INFO - data - data.py:1251 - Grouped data shape: (4, 4)
2025-06-04 18:32:11,637 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:32:11,638 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:32:11,639 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 19289.00 kWh
2025-06-04 18:32:11,639 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:32:11,640 - INFO - data - data.py:1028 - Getting ToD binned data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-09
2025-06-04 18:32:11,640 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:32:11,640 - INFO - data - data.py:841 - FIXED: Fetching 15-minute solar generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} with consistent settings
2025-06-04 18:32:12,036 - INFO - data - data.py:1465 - Smart cache returned 9 rows for Kids Clinic India Limited
2025-06-04 18:32:12,041 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.KIDS, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:32:20,245 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:32:20,245 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:32:20,488 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:32:20,774 - INFO - data - data.py:1465 - Smart cache returned 9 rows for Kids Clinic India Limited
2025-06-04 18:32:20,774 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.KIDS, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:32:21,556 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'ANS.Daily Energy']
2025-06-04 18:32:21,556 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-09T00:00:00Z+05:30', '1': '2025-04-09T00:15:00Z+05:30', '2': '2025-04-09T00:30:00Z+05:30'}, 'ANS.Daily Energy': {'0': 0.0, '1': 0.0, '2': 0.0}}
2025-06-04 18:32:21,573 - INFO - data - data.py:941 - Converting cumulative daily energy to 15-minute incremental values
2025-06-04 18:32:21,573 - INFO - data - data.py:949 - Converted cumulative to incremental: Total = 18848.47 kWh
2025-06-04 18:32:21,573 - INFO - data - data.py:957 - Successfully processed single day generation data for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}: 96 records
2025-06-04 18:32:21,573 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-09
2025-06-04 18:32:22,281 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:32:22,342 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 23252.47 kWh
2025-06-04 18:32:22,342 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:32:22,349 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 18848.47 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:22,349 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:32:22,349 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 18848.47 kWh
2025-06-04 18:32:22,349 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:32:22,349 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:32:22,349 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:32:22,349 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 18848.47 kWh
2025-06-04 18:32:22,349 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:32:22,360 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:32:22,360 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-09
2025-06-04 18:32:22,360 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-09
2025-06-04 18:32:22,964 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'}
2025-06-04 18:32:22,995 - WARNING - data - data.py:1127 - No consumption data found for {'name': 'ANS', 'plant_id': 'IN.INTE.ANS1'} on 2025-04-09
2025-06-04 18:32:22,995 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:32:22,995 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:32:22,995 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:32:22,995 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:32:22,995 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:32:22,995 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:32:22,995 - INFO - data - data.py:1156 - Before merge - Generation: 18848.47 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:22,995 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:32:22,995 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:32:22,995 - INFO - data - data.py:1176 - After merge - Generation: 18848.47 kWh, Consumption: 0.00 kWh
2025-06-04 18:32:22,995 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 18848.47 kWh, Total consumption: 0.00 kWh
2025-06-04 18:32:22,995 - INFO - data - data.py:1192 - ToD - Filtered out 10 rows with BOTH zero generation AND consumption
2025-06-04 18:32:22,995 - INFO - data - data.py:1196 - ToD - Using filtered data with 14 valid hours
2025-06-04 18:32:22,995 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:32:23,012 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 18848.47 kWh
2025-06-04 18:32:23,012 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:32:23,012 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:32:23,015 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 18848.47 kWh
2025-06-04 18:32:23,015 - INFO - data - data.py:1251 - Grouped data shape: (3, 4)
2025-06-04 18:32:23,015 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:32:23,019 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:32:23,019 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 18848.47 kWh
2025-06-04 18:32:23,019 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:33:55,064 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:33:55,065 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:33:55,281 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:33:55,579 - INFO - data - data.py:1465 - Smart cache returned 9 rows for Kids Clinic India Limited
2025-06-04 18:33:55,585 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.KIDS, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:35:16,839 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:35:16,839 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:35:17,064 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:35:17,318 - INFO - data - data.py:1465 - Smart cache returned 9 rows for Kids Clinic India Limited
2025-06-04 18:35:17,335 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.KIDS, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:35:17,968 - INFO - data - data.py:867 - FIXED: Fetching daily solar generation data for Kids Clinic India Limited date range with consistent settings
2025-06-04 18:35:32,270 - INFO - data - data.py:893 - API returned generation dataframe with shape: (9, 2) and columns: ['time', 'Kids Clinic India Limited.Daily Energy']
2025-06-04 18:35:32,270 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-04-01T00:00:00Z+05:30', '1': '2025-04-02T00:00:00Z+05:30', '2': '2025-04-03T00:00:00Z+05:30'}, 'Kids Clinic India Limited.Daily Energy': {'0': 29768, '1': 19035, '2': 20343}}
2025-06-04 18:35:32,270 - INFO - data - data.py:988 - Successfully processed date range generation data for Kids Clinic India Limited: 9 records
2025-06-04 18:36:47,508 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:36:47,508 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:36:47,724 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:36:49,708 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:36:49,712 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:36:49,997 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:36:50,029 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:36:50,034 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:36:50,308 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:36:50,360 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:36:50,361 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:36:50,361 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:36:50,361 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:37:04,786 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:37:04,805 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:37:04,805 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:37:30,210 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:37:30,217 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:37:30,444 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:37:32,353 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:37:32,360 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:37:32,595 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:37:32,654 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:37:32,658 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:37:32,910 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:37:32,949 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:37:32,950 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:37:32,950 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:37:32,951 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:37:49,019 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:37:49,019 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:37:49,019 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:38:08,309 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:38:08,309 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:38:08,543 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:38:10,440 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:38:10,446 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:38:10,700 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:38:10,737 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:38:10,744 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:38:11,009 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:38:11,052 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:38:11,052 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:38:11,052 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:38:11,052 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:38:27,815 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:38:27,832 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:38:27,832 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:38:41,634 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (96, 2)
2025-06-04 18:38:41,650 - INFO - data - data.py:2230 - Added 96 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:38:41,650 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:38:41,650 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:38:41,650 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:38:41,650 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 192 rows
2025-06-04 18:38:42,997 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: Combined View, date: 2025-01-01
2025-06-04 18:38:43,450 - WARNING - data - data.py:354 - No data found for plant_id Combined View, trying with Plant Long Name Combined View
2025-06-04 18:38:49,054 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:38:49,056 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:38:49,267 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:39:30,459 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:39:30,459 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:39:30,686 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:39:32,468 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:39:32,472 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:39:32,735 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:39:32,759 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:39:32,762 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:39:32,982 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:39:33,037 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:39:33,038 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:39:33,038 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:39:33,038 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:39:36,954 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:39:36,954 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:39:37,170 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:39:55,672 - INFO - data - data.py:1465 - Smart cache returned 96 rows for Avon Plastic Industries Pvt Ltd
2025-06-04 18:39:55,678 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:39:56,106 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:39:56,166 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 166630306209023.22 kWh
2025-06-04 18:39:56,166 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 2)
2025-06-04 18:39:56,172 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:40:11,739 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:40:11,739 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:40:11,979 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:40:13,860 - INFO - data - data.py:1465 - Smart cache returned 96 rows for Avon Plastic Industries Pvt Ltd
2025-06-04 18:40:13,864 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:40:14,089 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:40:14,140 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 166630306209023.22 kWh
2025-06-04 18:40:14,140 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 2)
2025-06-04 18:40:14,145 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:40:29,668 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'WTG4.Generation today']
2025-06-04 18:40:29,668 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-01-01T00:00:00Z+05:30', '1': '2025-01-01T00:15:00Z+05:30', '2': '2025-01-01T00:30:00Z+05:30'}, 'WTG4.Generation today': {'0': 71.9655049642, '1': 148.4814961751, '2': 207.5842590332}}
2025-06-04 18:40:29,678 - INFO - data - data.py:957 - Successfully processed single day generation data for Avon Plastic Industries Pvt Ltd: 96 records
2025-06-04 18:40:30,568 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:40:31,005 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:40:31,058 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 166630306209023.22 kWh
2025-06-04 18:40:31,058 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 2)
2025-06-04 18:40:31,064 - INFO - data - data.py:1028 - Getting ToD binned data for Avon Plastic Industries Pvt Ltd on 2025-01-01
2025-06-04 18:40:31,064 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:40:31,064 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:40:48,016 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'WTG4.Generation today']
2025-06-04 18:40:48,018 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-01-01T00:00:00Z+05:30', '1': '2025-01-01T00:15:00Z+05:30', '2': '2025-01-01T00:30:00Z+05:30'}, 'WTG4.Generation today': {'0': 71.9655049642, '1': 148.4814961751, '2': 207.5842590332}}
2025-06-04 18:40:48,026 - INFO - data - data.py:957 - Successfully processed single day generation data for Avon Plastic Industries Pvt Ltd: 96 records
2025-06-04 18:40:48,028 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:40:48,605 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:40:48,652 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 617831.20 kWh
2025-06-04 18:40:48,652 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 5)
2025-06-04 18:40:48,675 - INFO - data - data.py:565 - Generation vs Consumption - Final output totals - Generation: 613427.20 kWh, Consumption: 0.00 kWh
2025-06-04 18:40:48,675 - INFO - data - data.py:566 - Generation vs Consumption - Final output shape: (96, 4)
2025-06-04 18:40:48,675 - INFO - data - data.py:1052 - ToD - Using Summary tab data source - Total generation: 613427.20 kWh
2025-06-04 18:40:48,675 - INFO - data - data.py:1060 - ToD - CRITICAL FIX: Found 'hour' column with 96 records
2025-06-04 18:40:48,675 - INFO - data - data.py:1062 - ToD - CRITICAL FIX: Aggregating 96 records to hourly (>24 rows detected)
2025-06-04 18:40:48,675 - INFO - data - data.py:1064 - ToD - CRITICAL FIX: Successfully aggregated to hourly: 24 records
2025-06-04 18:40:48,678 - INFO - data - data.py:1080 - ToD - After hourly processing - Total generation: 613427.20 kWh
2025-06-04 18:40:48,678 - INFO - data - data.py:1085 - Retrieved 24 rows of generation data for ToD binning
2025-06-04 18:40:48,678 - INFO - data - data.py:1086 - Generation data columns: ['hour', 'generation_kwh']
2025-06-04 18:40:48,678 - INFO - data - data.py:1094 - Getting consumption data for ToD binning for Avon Plastic Industries Pvt Ltd on 2025-01-01
2025-06-04 18:40:48,678 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:40:49,192 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:40:49,221 - WARNING - data - data.py:1127 - No consumption data found for Avon Plastic Industries Pvt Ltd on 2025-01-01
2025-06-04 18:40:49,221 - INFO - data - data.py:1130 - Created empty consumption DataFrame with zeros
2025-06-04 18:40:49,221 - INFO - data - data.py:1137 - Merging generation and consumption data for ToD binning
2025-06-04 18:40:49,221 - INFO - data - data.py:1139 - Generation DataFrame columns: ['hour', 'generation_kwh']
2025-06-04 18:40:49,221 - INFO - data - data.py:1140 - Consumption DataFrame columns: ['hour', 'energy_kwh', 'plant_long_name']
2025-06-04 18:40:49,221 - INFO - data - data.py:1141 - Generation DataFrame shape: (24, 2)
2025-06-04 18:40:49,221 - INFO - data - data.py:1142 - Consumption DataFrame shape: (24, 3)
2025-06-04 18:40:49,221 - INFO - data - data.py:1156 - Before merge - Generation: 613427.20 kWh, Consumption: 0.00 kWh
2025-06-04 18:40:49,221 - INFO - data - data.py:1167 - Merged data shape: (24, 4)
2025-06-04 18:40:49,225 - INFO - data - data.py:1168 - Merged data columns: ['hour', 'generation_kwh', 'energy_kwh', 'plant_long_name']
2025-06-04 18:40:49,225 - INFO - data - data.py:1176 - After merge - Generation: 613427.20 kWh, Consumption: 0.00 kWh
2025-06-04 18:40:49,225 - INFO - data - data.py:1191 - ToD - After filtering - Total generation: 613427.20 kWh, Total consumption: 0.00 kWh
2025-06-04 18:40:49,225 - INFO - data - data.py:1192 - ToD - Filtered out 0 rows with BOTH zero generation AND consumption
2025-06-04 18:40:49,225 - INFO - data - data.py:1196 - ToD - Using filtered data with 24 valid hours
2025-06-04 18:40:49,225 - INFO - data - data.py:1223 - Assigning ToD bins to data
2025-06-04 18:40:49,225 - INFO - data - data.py:1232 - ToD - Before ToD bin aggregation - Total generation: 613427.20 kWh
2025-06-04 18:40:49,225 - INFO - data - data.py:1234 - Grouping data by ToD bin
2025-06-04 18:40:49,225 - INFO - data - data.py:1242 - Aggregation columns: {'generation_kwh': 'sum', 'energy_kwh': 'sum', 'is_peak': 'first'}
2025-06-04 18:40:49,229 - INFO - data - data.py:1249 - ToD - After ToD bin aggregation - Total generation: 613427.20 kWh
2025-06-04 18:40:49,229 - INFO - data - data.py:1251 - Grouped data shape: (4, 4)
2025-06-04 18:40:49,229 - INFO - data - data.py:1252 - Grouped data columns: ['tod_bin', 'generation_kwh', 'energy_kwh', 'is_peak']
2025-06-04 18:40:49,233 - INFO - data - data.py:1321 - Renamed energy_kwh to consumption_kwh for ToD binned data
2025-06-04 18:40:49,234 - INFO - data - data.py:1325 - ToD - FINAL RESULT - Total generation: 613427.20 kWh
2025-06-04 18:40:49,234 - INFO - data - data.py:1326 - ToD - SUCCESS: Data processing completed with consistent totals
2025-06-04 18:40:49,763 - INFO - data - data.py:1028 - Getting ToD binned data for Avon Plastic Industries Pvt Ltd on 2025-01-01
2025-06-04 18:40:49,763 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:40:49,763 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:41:01,238 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:41:01,238 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:41:01,455 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:41:03,120 - INFO - data - data.py:1465 - Smart cache returned 96 rows for Avon Plastic Industries Pvt Ltd
2025-06-04 18:41:03,126 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:41:03,306 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:41:03,307 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:41:03,309 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:41:03,310 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:41:03,393 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:41:03,437 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 166630306209023.22 kWh
2025-06-04 18:41:03,438 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 2)
2025-06-04 18:41:03,443 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:41:24,145 - INFO - data - data.py:893 - API returned generation dataframe with shape: (96, 2) and columns: ['time', 'WTG4.Generation today']
2025-06-04 18:41:24,155 - INFO - data - data.py:895 - First few rows of generation data: {'time': {'0': '2025-01-01T00:00:00Z+05:30', '1': '2025-01-01T00:15:00Z+05:30', '2': '2025-01-01T00:30:00Z+05:30'}, 'WTG4.Generation today': {'0': 71.9655049642, '1': 148.4814961751, '2': 207.5842590332}}
2025-06-04 18:41:24,160 - INFO - data - data.py:957 - Successfully processed single day generation data for Avon Plastic Industries Pvt Ltd: 96 records
2025-06-04 18:41:24,628 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.APIP, date: 2025-01-01
2025-06-04 18:41:24,878 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.APIP, trying with Plant Long Name Avon Plastic Industries Pvt Ltd
2025-06-04 18:41:24,901 - INFO - data - data.py:485 - Generation vs Consumption - Input generation total: 166630306209023.22 kWh
2025-06-04 18:41:24,901 - INFO - data - data.py:486 - Generation vs Consumption - Generation data shape: (96, 2)
2025-06-04 18:41:24,901 - INFO - data - data.py:1028 - Getting ToD binned data for Avon Plastic Industries Pvt Ltd on 2025-01-01
2025-06-04 18:41:24,901 - INFO - data - data.py:1034 - FIXED: Using identical data source as Summary tab for consistency
2025-06-04 18:41:24,901 - INFO - data - data.py:853 - FIXED: Fetching 15-minute wind generation data for Avon Plastic Industries Pvt Ltd with consistent settings
2025-06-04 18:41:29,322 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:41:29,330 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:41:29,330 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:42:40,096 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:42:40,096 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:42:40,314 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:42:42,319 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:42:42,323 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:42:42,559 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:42:42,589 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:42:42,589 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:42:42,841 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:42:42,903 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:42:42,903 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:42:42,904 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:42:42,904 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:42:57,985 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:42:57,994 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:42:57,994 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:43:12,456 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (96, 2)
2025-06-04 18:43:12,460 - INFO - data - data.py:2230 - Added 96 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:43:12,462 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:43:12,462 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:43:12,462 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:43:12,462 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 192 rows
2025-06-04 18:43:51,403 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:43:51,403 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:43:51,653 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:43:53,606 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:43:53,611 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:43:53,848 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:43:53,876 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:43:53,879 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:43:54,121 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:43:54,180 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:43:54,180 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:43:54,180 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:43:54,181 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:44:03,787 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (96, 2)
2025-06-04 18:44:03,798 - INFO - data - data.py:2155 - Added 96 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:44:03,798 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-01-01 to 2025-01-01 with 15m granularity
2025-06-04 18:44:14,152 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (96, 2)
2025-06-04 18:44:14,175 - INFO - data - data.py:2230 - Added 96 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:44:14,177 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:44:14,177 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:44:14,177 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:44:14,177 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 192 rows
2025-06-04 18:44:40,482 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:44:40,482 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:44:40,721 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:45:03,575 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:45:03,575 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:45:03,807 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:45:05,673 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:45:05,677 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:45:05,906 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:45:05,932 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:45:05,938 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:45:06,185 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:45:06,231 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:45:06,232 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:45:06,232 - INFO - data - data.py:2078 - Using 15-minute granularity for single day view: 2025-01-01
2025-06-04 18:45:06,232 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-01-01 to 2025-01-01 with 15m granularity
