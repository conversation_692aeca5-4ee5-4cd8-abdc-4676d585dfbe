2025-06-04 18:33:56,081 - INFO - display_components - display_components.py:1205 - Available columns in daily comparison data: ['time', 'kids clinic india limited.daily energy', 'consumption_kwh']
2025-06-04 18:33:56,081 - WARNING - display_components - display_components.py:1229 - Cannot calculate surplus generation/demand. Missing columns. Available: ['time', 'kids clinic india limited.daily energy', 'consumption_kwh']
2025-06-04 18:33:56,081 - WARNING - display_components - display_components.py:1239 - Cannot calculate metrics - missing required columns
2025-06-04 18:35:17,768 - INFO - display_components - display_components.py:1205 - Available columns in daily comparison data: ['time', 'kids clinic india limited.daily energy', 'consumption_kwh']
2025-06-04 18:35:17,768 - INFO - display_components - display_components.py:1213 - Renamed kids clinic india limited.daily energy to generation_kwh
2025-06-04 18:35:17,768 - INFO - display_components - display_components.py:1271 - Multiple days - Time-based lapsed units (after loss): 188764.34 kWh
2025-06-04 18:51:13,364 - INFO - display_components - display_components.py:1380 - ToD plot - Total generation: 31726.50 kWh, Total consumption: 22913.54 kWh
